import * as SQLite from 'expo-sqlite';

export interface Candy {
  id: number;
  name: string;
  points: number;
  createdAt: string;
  updatedAt: string;
}

export interface Task {
  id: number;
  name: string;
  points: number;
  createdAt: string;
  updatedAt: string;
}

export interface Record {
  id: number;
  type: 'candy' | 'task';
  itemId: number;
  itemName: string;
  points: number;
  note?: string;
  createdAt: string;
}

class Database {
  private db: SQLite.SQLiteDatabase | null = null;

  async init() {
    this.db = await SQLite.openDatabaseAsync('candyv2.db');
    await this.createTables();
  }

  private async createTables() {
    if (!this.db) throw new Error('Database not initialized');

    // 创建糖果表
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS candies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        points INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建任务表
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        points INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建行为记录表
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK (type IN ('candy', 'task')),
        item_id INTEGER NOT NULL,
        item_name TEXT NOT NULL,
        points INTEGER NOT NULL,
        note TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);
  }

  // 糖果相关操作
  async createCandy(name: string, points: number): Promise<Candy> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.runAsync(
      'INSERT INTO candies (name, points) VALUES (?, ?)',
      [name, points]
    );

    const candy = await this.getCandyById(result.lastInsertRowId);
    if (!candy) throw new Error('Failed to create candy');
    return candy;
  }

  async getCandies(): Promise<Candy[]> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getAllAsync(
      'SELECT * FROM candies ORDER BY created_at DESC'
    );

    return result.map((row: any) => ({
      id: row.id as number,
      name: row.name as string,
      points: row.points as number,
      createdAt: row.created_at as string,
      updatedAt: row.updated_at as string,
    }));
  }

  async getCandyById(id: number): Promise<Candy | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(
      'SELECT * FROM candies WHERE id = ?',
      [id]
    );

    if (!result) return null;

    return {
      id: (result as any).id as number,
      name: (result as any).name as string,
      points: (result as any).points as number,
      createdAt: (result as any).created_at as string,
      updatedAt: (result as any).updated_at as string,
    };
  }

  async updateCandy(id: number, name: string, points: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      'UPDATE candies SET name = ?, points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, points, id]
    );
  }

  async deleteCandy(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync('DELETE FROM candies WHERE id = ?', [id]);
  }

  // 任务相关操作
  async createTask(name: string, points: number): Promise<Task> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.runAsync(
      'INSERT INTO tasks (name, points) VALUES (?, ?)',
      [name, points]
    );

    const task = await this.getTaskById(result.lastInsertRowId);
    if (!task) throw new Error('Failed to create task');
    return task;
  }

  async getTasks(): Promise<Task[]> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getAllAsync(
      'SELECT * FROM tasks ORDER BY created_at DESC'
    );

    return result.map((row: any) => ({
      id: row.id as number,
      name: row.name as string,
      points: row.points as number,
      createdAt: row.created_at as string,
      updatedAt: row.updated_at as string,
    }));
  }

  async getTaskById(id: number): Promise<Task | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(
      'SELECT * FROM tasks WHERE id = ?',
      [id]
    );

    if (!result) return null;

    return {
      id: (result as any).id as number,
      name: (result as any).name as string,
      points: (result as any).points as number,
      createdAt: (result as any).created_at as string,
      updatedAt: (result as any).updated_at as string,
    };
  }

  async updateTask(id: number, name: string, points: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      'UPDATE tasks SET name = ?, points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, points, id]
    );
  }

  async deleteTask(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync('DELETE FROM tasks WHERE id = ?', [id]);
  }

  // 行为记录相关操作
  async createRecord(type: 'candy' | 'task', itemId: number, itemName: string, points: number, note?: string): Promise<Record> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.runAsync(
      'INSERT INTO records (type, item_id, item_name, points, note) VALUES (?, ?, ?, ?, ?)',
      [type, itemId, itemName, points, note || null]
    );

    const record = await this.getRecordById(result.lastInsertRowId);
    if (!record) throw new Error('Failed to create record');
    return record;
  }

  async getRecords(): Promise<Record[]> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getAllAsync(
      'SELECT * FROM records ORDER BY created_at DESC'
    );

    return result.map((row: any) => ({
      id: row.id as number,
      type: row.type as 'candy' | 'task',
      itemId: row.item_id as number,
      itemName: row.item_name as string,
      points: row.points as number,
      note: row.note as string || undefined,
      createdAt: row.created_at as string,
    }));
  }

  async getRecordById(id: number): Promise<Record | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(
      'SELECT * FROM records WHERE id = ?',
      [id]
    );

    if (!result) return null;

    return {
      id: (result as any).id as number,
      type: (result as any).type as 'candy' | 'task',
      itemId: (result as any).item_id as number,
      itemName: (result as any).item_name as string,
      points: (result as any).points as number,
      note: (result as any).note as string || undefined,
      createdAt: (result as any).created_at as string,
    };
  }

  async deleteRecord(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync('DELETE FROM records WHERE id = ?', [id]);
  }

  // 计算总积分
  async getTotalPoints(): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync(`
      SELECT
        COALESCE(SUM(CASE WHEN type = 'task' THEN points ELSE -points END), 0) as total
      FROM records
    `);

    return ((result as any)?.total as number) || 0;
  }
}

export const database = new Database();
