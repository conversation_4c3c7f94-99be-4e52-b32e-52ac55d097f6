import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/AppNavigator';
import { database } from '../database/database';
import { theme } from '../styles/theme';
import i18n from '../i18n';

type CreateCandyScreenNavigationProp = StackNavigationProp<RootStackParamList, 'CreateCandy'>;
type CreateCandyScreenRouteProp = RouteProp<RootStackParamList, 'CreateCandy'>;

interface Props {
  navigation: CreateCandyScreenNavigationProp;
  route: CreateCandyScreenRouteProp;
}

export default function CreateCandyScreen({ navigation, route }: Props) {
  const [name, setName] = useState('');
  const [points, setPoints] = useState('');
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const candyId = route.params?.candyId;

  useEffect(() => {
    if (candyId) {
      setIsEditing(true);
      loadCandy();
    }
  }, [candyId]);

  const loadCandy = async () => {
    if (!candyId) return;
    
    try {
      const candy = await database.getCandyById(candyId);
      if (candy) {
        setName(candy.name);
        setPoints(candy.points.toString());
      }
    } catch (error) {
      console.error('Error loading candy:', error);
      Alert.alert(i18n.t('common.error'), 'Failed to load candy');
    }
  };

  const handleSave = async () => {
    if (!name.trim()) {
      Alert.alert(i18n.t('common.error'), i18n.t('candy.enterName'));
      return;
    }

    const pointsValue = parseInt(points);
    if (isNaN(pointsValue) || pointsValue <= 0) {
      Alert.alert(i18n.t('common.error'), i18n.t('candy.enterPoints'));
      return;
    }

    setLoading(true);
    try {
      if (isEditing && candyId) {
        await database.updateCandy(candyId, name.trim(), pointsValue);
      } else {
        await database.createCandy(name.trim(), pointsValue);
      }
      navigation.goBack();
    } catch (error) {
      console.error('Error saving candy:', error);
      Alert.alert(i18n.t('common.error'), 'Failed to save candy');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{i18n.t('candy.name')}</Text>
            <TextInput
              style={styles.input}
              placeholder={i18n.t('candy.enterName')}
              value={name}
              onChangeText={setName}
              autoFocus
              returnKeyType="next"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>{i18n.t('candy.points')}</Text>
            <TextInput
              style={styles.input}
              placeholder={i18n.t('candy.enterPoints')}
              value={points}
              onChangeText={setPoints}
              keyboardType="numeric"
              returnKeyType="done"
              onSubmitEditing={handleSave}
            />
            <Text style={styles.hint}>糖果会减少积分</Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.saveButton,
            (!name.trim() || !points.trim() || loading) && styles.saveButtonDisabled
          ]}
          onPress={handleSave}
          disabled={!name.trim() || !points.trim() || loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? i18n.t('common.loading') : i18n.t('common.save')}
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
  },
  form: {
    padding: theme.spacing.md,
  },
  inputGroup: {
    marginBottom: theme.spacing.lg,
  },
  label: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    fontSize: theme.fontSize.md,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
    ...theme.shadows.small,
  },
  hint: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
    fontStyle: 'italic',
  },
  footer: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    gap: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.textSecondary,
  },
  saveButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.error,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: theme.colors.disabled,
  },
  saveButtonText: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.surface,
  },
});
