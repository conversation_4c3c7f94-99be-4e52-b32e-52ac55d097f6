import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { database, Record } from '../database/database';
import { theme } from '../styles/theme';
import i18n from '../i18n';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MainTabs'>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

export default function HomeScreen({ navigation }: Props) {
  const [records, setRecords] = useState<Record[]>([]);
  const [totalPoints, setTotalPoints] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  const loadData = async () => {
    try {
      const [recordsData, pointsData] = await Promise.all([
        database.getRecords(),
        database.getTotalPoints(),
      ]);
      setRecords(recordsData);
      setTotalPoints(pointsData);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert(i18n.t('common.error'), 'Failed to load data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const handleDeleteRecord = (recordId: number) => {
    Alert.alert(
      i18n.t('common.confirm'),
      'Are you sure you want to delete this record?',
      [
        { text: i18n.t('common.cancel'), style: 'cancel' },
        {
          text: i18n.t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await database.deleteRecord(recordId);
              loadData();
            } catch (error) {
              console.error('Error deleting record:', error);
              Alert.alert(i18n.t('common.error'), 'Failed to delete record');
            }
          },
        },
      ]
    );
  };

  const renderRecord = ({ item }: { item: Record }) => {
    const isCandy = item.type === 'candy';
    const pointsColor = isCandy ? theme.colors.error : theme.colors.success;
    const pointsText = isCandy ? `-${item.points}` : `+${item.points}`;
    const iconName = isCandy ? 'remove-circle' : 'add-circle';

    return (
      <TouchableOpacity
        style={styles.recordItem}
        onLongPress={() => handleDeleteRecord(item.id)}
      >
        <View style={styles.recordHeader}>
          <View style={styles.recordInfo}>
            <Ionicons name={iconName} size={24} color={pointsColor} />
            <View style={styles.recordText}>
              <Text style={styles.recordName}>{item.itemName}</Text>
              <Text style={styles.recordType}>
                {i18n.t(isCandy ? 'record.candy' : 'record.task')}
              </Text>
            </View>
          </View>
          <Text style={[styles.recordPoints, { color: pointsColor }]}>
            {pointsText}
          </Text>
        </View>
        {item.note && (
          <Text style={styles.recordNote}>{item.note}</Text>
        )}
        <Text style={styles.recordDate}>
          {new Date(item.createdAt).toLocaleString()}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.pointsCard}>
        <Text style={styles.pointsLabel}>{i18n.t('home.totalPoints')}</Text>
        <Text style={styles.pointsValue}>{totalPoints}</Text>
      </View>

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => navigation.navigate('CreateRecord')}
      >
        <Ionicons name="add" size={24} color={theme.colors.surface} />
        <Text style={styles.addButtonText}>{i18n.t('home.addRecord')}</Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="document-outline" size={64} color={theme.colors.textLight} />
      <Text style={styles.emptyText}>{i18n.t('home.noRecords')}</Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>{i18n.t('common.loading')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={records}
        renderItem={renderRecord}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: theme.spacing.lg,
  },
  header: {
    padding: theme.spacing.md,
  },
  pointsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    ...theme.shadows.medium,
  },
  pointsLabel: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  pointsValue: {
    fontSize: theme.fontSize.xxxl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.primary,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...theme.shadows.medium,
  },
  addButtonText: {
    color: theme.colors.surface,
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    marginLeft: theme.spacing.sm,
  },
  recordItem: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    ...theme.shadows.small,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  recordInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  recordText: {
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  recordName: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
  },
  recordType: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  recordPoints: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
  },
  recordNote: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
    fontStyle: 'italic',
  },
  recordDate: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textLight,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: theme.spacing.xxl,
  },
  emptyText: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
});
