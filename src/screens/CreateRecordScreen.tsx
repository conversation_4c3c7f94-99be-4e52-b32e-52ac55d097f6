import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ScrollView,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { database, Candy, Task } from '../database/database';
import { theme } from '../styles/theme';
import i18n from '../i18n';

type CreateRecordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'CreateRecord'>;

interface Props {
  navigation: CreateRecordScreenNavigationProp;
}

export default function CreateRecordScreen({ navigation }: Props) {
  const [selectedType, setSelectedType] = useState<'candy' | 'task' | null>(null);
  const [candies, setCandies] = useState<Candy[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedItem, setSelectedItem] = useState<Candy | Task | null>(null);
  const [note, setNote] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [candiesData, tasksData] = await Promise.all([
        database.getCandies(),
        database.getTasks(),
      ]);
      setCandies(candiesData);
      setTasks(tasksData);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert(i18n.t('common.error'), 'Failed to load data');
    }
  };

  const handleCreateRecord = async () => {
    if (!selectedType || !selectedItem) {
      Alert.alert(i18n.t('common.error'), i18n.t('record.selectType'));
      return;
    }

    setLoading(true);
    try {
      await database.createRecord(
        selectedType,
        selectedItem.id,
        selectedItem.name,
        selectedItem.points,
        note.trim() || undefined
      );
      navigation.goBack();
    } catch (error) {
      console.error('Error creating record:', error);
      Alert.alert(i18n.t('common.error'), 'Failed to create record');
    } finally {
      setLoading(false);
    }
  };

  const renderTypeSelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{i18n.t('record.type')}</Text>
      <View style={styles.typeContainer}>
        <TouchableOpacity
          style={[
            styles.typeButton,
            selectedType === 'candy' && styles.typeButtonSelected,
            { backgroundColor: selectedType === 'candy' ? theme.colors.error : theme.colors.surface }
          ]}
          onPress={() => {
            setSelectedType('candy');
            setSelectedItem(null);
          }}
        >
          <Ionicons
            name="remove-circle"
            size={24}
            color={selectedType === 'candy' ? theme.colors.surface : theme.colors.error}
          />
          <Text style={[
            styles.typeButtonText,
            { color: selectedType === 'candy' ? theme.colors.surface : theme.colors.error }
          ]}>
            {i18n.t('record.candy')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeButton,
            selectedType === 'task' && styles.typeButtonSelected,
            { backgroundColor: selectedType === 'task' ? theme.colors.success : theme.colors.surface }
          ]}
          onPress={() => {
            setSelectedType('task');
            setSelectedItem(null);
          }}
        >
          <Ionicons
            name="add-circle"
            size={24}
            color={selectedType === 'task' ? theme.colors.surface : theme.colors.success}
          />
          <Text style={[
            styles.typeButtonText,
            { color: selectedType === 'task' ? theme.colors.surface : theme.colors.success }
          ]}>
            {i18n.t('record.task')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderItemSelector = () => {
    if (!selectedType) return null;

    const items = selectedType === 'candy' ? candies : tasks;
    const emptyMessage = selectedType === 'candy' ? 'No candies available' : 'No tasks available';
    const createButtonText = selectedType === 'candy' ? i18n.t('candy.create') : i18n.t('task.create');
    const manageRoute = selectedType === 'candy' ? 'ManageCandies' : 'ManageTasks';

    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            {selectedType === 'candy' ? i18n.t('candy.title') : i18n.t('task.title')}
          </Text>
          <TouchableOpacity
            style={styles.manageButton}
            onPress={() => navigation.navigate(manageRoute)}
          >
            <Text style={styles.manageButtonText}>管理</Text>
          </TouchableOpacity>
        </View>

        {items.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>{emptyMessage}</Text>
            <TouchableOpacity
              style={styles.createButton}
              onPress={() => navigation.navigate(selectedType === 'candy' ? 'CreateCandy' : 'CreateTask', {})}
            >
              <Ionicons name="add" size={20} color={theme.colors.surface} />
              <Text style={styles.createButtonText}>{createButtonText}</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={items}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.itemButton,
                  selectedItem?.id === item.id && styles.itemButtonSelected
                ]}
                onPress={() => setSelectedItem(item)}
              >
                <View style={styles.itemInfo}>
                  <Text style={styles.itemName}>{item.name}</Text>
                  <Text style={[
                    styles.itemPoints,
                    { color: selectedType === 'candy' ? theme.colors.error : theme.colors.success }
                  ]}>
                    {selectedType === 'candy' ? `-${item.points}` : `+${item.points}`}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
            scrollEnabled={false}
          />
        )}
      </View>
    );
  };

  const renderNoteInput = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{i18n.t('record.note')}</Text>
      <TextInput
        style={styles.noteInput}
        placeholder={i18n.t('record.enterNote')}
        value={note}
        onChangeText={setNote}
        multiline
        numberOfLines={3}
        textAlignVertical="top"
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderTypeSelector()}
        {renderItemSelector()}
        {renderNoteInput()}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.saveButton,
            (!selectedType || !selectedItem || loading) && styles.saveButtonDisabled
          ]}
          onPress={handleCreateRecord}
          disabled={!selectedType || !selectedItem || loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? i18n.t('common.loading') : i18n.t('common.save')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: theme.spacing.md,
  },
  section: {
    marginBottom: theme.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
  },
  manageButton: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.sm,
  },
  manageButtonText: {
    color: theme.colors.surface,
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.medium,
  },
  typeContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 2,
    borderColor: 'transparent',
    ...theme.shadows.small,
  },
  typeButtonSelected: {
    borderColor: theme.colors.primary,
  },
  typeButtonText: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    marginLeft: theme.spacing.sm,
  },
  itemButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    borderWidth: 2,
    borderColor: 'transparent',
    ...theme.shadows.small,
  },
  itemButtonSelected: {
    borderColor: theme.colors.primary,
  },
  itemInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemName: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.text,
    flex: 1,
  },
  itemPoints: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.bold,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  emptyText: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.md,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
  },
  createButtonText: {
    color: theme.colors.surface,
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.medium,
    marginLeft: theme.spacing.xs,
  },
  noteInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    fontSize: theme.fontSize.md,
    color: theme.colors.text,
    minHeight: 80,
    ...theme.shadows.small,
  },
  footer: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    gap: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  cancelButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.textSecondary,
  },
  saveButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: theme.colors.disabled,
  },
  saveButtonText: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.surface,
  },
});
