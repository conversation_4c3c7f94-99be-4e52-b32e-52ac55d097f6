import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/AppNavigator';
import { database, Candy } from '../database/database';
import { theme } from '../styles/theme';
import i18n from '../i18n';

type ManageCandiesScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ManageCandies'>;

interface Props {
  navigation: ManageCandiesScreenNavigationProp;
}

export default function ManageCandiesScreen({ navigation }: Props) {
  const [candies, setCandies] = useState<Candy[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  const loadCandies = async () => {
    try {
      const candiesData = await database.getCandies();
      setCandies(candiesData);
    } catch (error) {
      console.error('Error loading candies:', error);
      Alert.alert(i18n.t('common.error'), 'Failed to load candies');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadCandies();
    }, [])
  );

  const onRefresh = () => {
    setRefreshing(true);
    loadCandies();
  };

  const handleDeleteCandy = (candy: Candy) => {
    Alert.alert(
      i18n.t('common.confirm'),
      `Are you sure you want to delete "${candy.name}"?`,
      [
        { text: i18n.t('common.cancel'), style: 'cancel' },
        {
          text: i18n.t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await database.deleteCandy(candy.id);
              loadCandies();
            } catch (error) {
              console.error('Error deleting candy:', error);
              Alert.alert(i18n.t('common.error'), 'Failed to delete candy');
            }
          },
        },
      ]
    );
  };

  const renderCandy = ({ item }: { item: Candy }) => (
    <View style={styles.candyItem}>
      <View style={styles.candyInfo}>
        <Text style={styles.candyName}>{item.name}</Text>
        <Text style={styles.candyPoints}>-{item.points} {i18n.t('candy.points')}</Text>
      </View>
      <View style={styles.candyActions}>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => navigation.navigate('CreateCandy', { candyId: item.id })}
        >
          <Ionicons name="pencil" size={20} color={theme.colors.primary} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteCandy(item)}
        >
          <Ionicons name="trash" size={20} color={theme.colors.error} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="candy-outline" size={64} color={theme.colors.textLight} />
      <Text style={styles.emptyText}>No candies yet</Text>
      <TouchableOpacity
        style={styles.createButton}
        onPress={() => navigation.navigate('CreateCandy', {})}
      >
        <Ionicons name="add" size={20} color={theme.colors.surface} />
        <Text style={styles.createButtonText}>{i18n.t('candy.create')}</Text>
      </TouchableOpacity>
    </View>
  );

  React.useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('CreateCandy', {})}
        >
          <Ionicons name="add" size={24} color={theme.colors.surface} />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>{i18n.t('common.loading')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={candies}
        renderItem={renderCandy}
        keyExtractor={(item) => item.id.toString()}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
  listContent: {
    flexGrow: 1,
    padding: theme.spacing.md,
  },
  candyItem: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...theme.shadows.small,
  },
  candyInfo: {
    flex: 1,
  },
  candyName: {
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: 4,
  },
  candyPoints: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.error,
    fontWeight: theme.fontWeight.medium,
  },
  candyActions: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  editButton: {
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.background,
  },
  deleteButton: {
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.background,
  },
  headerButton: {
    marginRight: theme.spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: theme.spacing.xxl,
  },
  emptyText: {
    fontSize: theme.fontSize.md,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.medium,
  },
  createButtonText: {
    color: theme.colors.surface,
    fontSize: theme.fontSize.md,
    fontWeight: theme.fontWeight.semibold,
    marginLeft: theme.spacing.sm,
  },
});
