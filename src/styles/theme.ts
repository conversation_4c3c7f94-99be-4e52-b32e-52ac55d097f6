// 彩色糖果主题配置
export const theme = {
  colors: {
    // 主色调 - 粉红色系
    primary: '#FF6B9D',
    primaryLight: '#FFB3D1',
    primaryDark: '#E91E63',
    
    // 辅助色 - 彩虹糖果色
    secondary: '#4ECDC4',
    accent: '#45B7D1',
    warning: '#FFA726',
    success: '#66BB6A',
    error: '#EF5350',
    
    // 糖果色彩板
    candyColors: {
      pink: '#FF6B9D',
      purple: '#9C27B0',
      blue: '#2196F3',
      cyan: '#00BCD4',
      teal: '#009688',
      green: '#4CAF50',
      lime: '#8BC34A',
      yellow: '#FFEB3B',
      orange: '#FF9800',
      red: '#F44336',
    },
    
    // 背景色
    background: '#FAFAFA',
    surface: '#FFFFFF',
    card: '#FFFFFF',
    
    // 文字色
    text: '#212121',
    textSecondary: '#757575',
    textLight: '#BDBDBD',
    
    // 边框色
    border: '#E0E0E0',
    divider: '#EEEEEE',
    
    // 状态色
    disabled: '#BDBDBD',
    placeholder: '#9E9E9E',
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    round: 50,
  },
  
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.18,
      shadowRadius: 1.0,
      elevation: 1,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.30,
      shadowRadius: 4.65,
      elevation: 8,
    },
  },
};

export type Theme = typeof theme;
