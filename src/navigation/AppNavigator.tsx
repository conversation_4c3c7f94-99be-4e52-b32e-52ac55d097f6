import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import i18n from '../i18n';

// 导入页面组件
import HomeScreen from '../screens/HomeScreen';
import CalendarScreen from '../screens/CalendarScreen';
import ProfileScreen from '../screens/ProfileScreen';
import CreateRecordScreen from '../screens/CreateRecordScreen';
import ManageCandiesScreen from '../screens/ManageCandiesScreen';
import ManageTasksScreen from '../screens/ManageTasksScreen';
import CreateCandyScreen from '../screens/CreateCandyScreen';
import CreateTaskScreen from '../screens/CreateTaskScreen';

export type RootStackParamList = {
  MainTabs: undefined;
  CreateRecord: undefined;
  ManageCandies: undefined;
  ManageTasks: undefined;
  CreateCandy: { candyId?: number };
  CreateTask: { taskId?: number };
};

export type TabParamList = {
  Calendar: undefined;
  Home: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();
const Stack = createStackNavigator<RootStackParamList>();

function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Calendar') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF6B9D',
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#E5E5EA',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerStyle: {
          backgroundColor: '#FF6B9D',
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Calendar" 
        component={CalendarScreen}
        options={{
          title: i18n.t('navigation.calendar'),
        }}
      />
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          title: i18n.t('navigation.home'),
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          title: i18n.t('navigation.profile'),
        }}
      />
    </Tab.Navigator>
  );
}

export default function AppNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: '#FF6B9D',
          },
          headerTintColor: '#FFFFFF',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen 
          name="MainTabs" 
          component={TabNavigator}
          options={{ headerShown: false }}
        />
        <Stack.Screen 
          name="CreateRecord" 
          component={CreateRecordScreen}
          options={{
            title: i18n.t('record.create'),
            presentation: 'modal',
          }}
        />
        <Stack.Screen 
          name="ManageCandies" 
          component={ManageCandiesScreen}
          options={{
            title: i18n.t('candy.title'),
          }}
        />
        <Stack.Screen 
          name="ManageTasks" 
          component={ManageTasksScreen}
          options={{
            title: i18n.t('task.title'),
          }}
        />
        <Stack.Screen 
          name="CreateCandy" 
          component={CreateCandyScreen}
          options={({ route }) => ({
            title: route.params?.candyId ? i18n.t('candy.edit') : i18n.t('candy.create'),
            presentation: 'modal',
          })}
        />
        <Stack.Screen 
          name="CreateTask" 
          component={CreateTaskScreen}
          options={({ route }) => ({
            title: route.params?.taskId ? i18n.t('task.edit') : i18n.t('task.create'),
            presentation: 'modal',
          })}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
